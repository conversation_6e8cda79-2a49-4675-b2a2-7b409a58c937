<?php

use Google\Service\Oauth2;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property MsRole $msrole
 * @property PasswordRequestReset $passwordrequestreset
 * @property MsRole $msrole
 * @property HistoryLogin $historylogin
 */
class Auth extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('PasswordRequestReset', 'passwordrequestreset');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('HistoryLogin', 'historylogin');
    }

    public function login()
    {
        $code = getGet('code');
        $success = getGet('success');
        $failed = getGet('failed');

        if (isLogin()) {
            return redirect(base_url('dashboard'));
        }

        $google_client = new Google_Client();
        $google_client->setClientId(String_Helper::GOOGLE_CLIENT_ID);
        $google_client->setClientSecret(String_Helper::GOOGLE_CLIENT_SECRET);
        $google_client->setRedirectUri(base_url(uri_string()));

        $google_client->addScope('email');
        $google_client->addScope('profile');

        if ($code != null) {
            $token = $google_client->fetchAccessTokenWithAuthCode($code);

            if (!isset($token['error'])) {
                $google_client->setAccessToken($token['access_token']);

                $google_service = new Oauth2($google_client);
                $data = $google_service->userinfo->get();

                $email = $data->getEmail();

                $get = $this->msusers->get(array(
                    'email' => $email,
                    'merchantid' => null
                ));

                if ($get->num_rows() == 0) {
                    return redirect(base_url('auth/login?failed=Alamat email yang anda masukkan tidak terdaftar'));
                }

                $row = $get->row();

                if ($row->isemailverified == null || $row->isemailverified == 0) {
                    $this->msusers->update(array(
                        'id' => $row->id
                    ), array(
                        'isemailverified' => 1
                    ));
                }

                // Log history login for Google login
                $this->historylogin->insert(array(
                    'userid' => $row->id,
                    'ipaddress' => getIPAddress(),
                    'useragent' => getUserAgent(),
                    'createddate' => getCurrentDate()
                ));

                setSessionValue(array(
                    'ISLOGIN' => true,
                    'USERID' => $row->id,
                    'NAME' => $row->name,
                    'EMAIL' => $row->email,
                    'ROLE' => $row->role
                ));

                return redirect(base_url('dashboard'));
            }
        }

        $data = array();
        $data['loginwithgoogle'] = $google_client->createAuthUrl();
        $data['success'] = removeSymbol($success);
        $data['failed'] = removeSymbol($failed);

        return $this->load->view('auth/login', $data);
    }

    public function process_login()
    {
        try {
            if (isLogin()) {
                return JSONResponseDefault('OK', 'Login berhasil');
            }

            if (!get_recaptcha_response()) {
                throw new Exception('Silahkan konfirmasi bahwa anda bukanlah Robot');
            }

            $email = getPost('email');
            $password = getPost('password');

            if ($email == null) {
                throw new Exception('Alamat email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Alamat email tidak valid');
            } else if ($password == null) {
                throw new Exception('Kata sandi wajib diisi');
            }

            $get = $this->msusers->get(array(
                'email' => $email,
                'merchantid' => null,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Alamat email yang anda masukkan tidak terdaftar');
            }

            $row = $get->row();

            if (!password_verify($password, $row->password)) {
                throw new Exception('Kata sandi yang anda masukkan salah');
            }

            if ($row->isdeleted == 1) {
                throw new Exception('Akun anda telah dihapus');
            }

            if ($row->role != 'Admin') {
                if ($row->isemailverified != 1) {
                    throw new Exception('Email anda belum terverifikasi');
                }
            }

            // Log login activity
            $this->historylogin->insert(array(
                'userid' => $row->id,
                'ipaddress' => getIPAddress(),
                'useragent' => getUserAgent(),
                'createddate' => getCurrentDate()
            ));

            setSessionValue(array(
                'ISLOGIN' => true,
                'USERID' => $row->id,
                'NAME' => $row->name,
                'EMAIL' => $row->email,
                'ROLE' => $row->role,
            ));

            return JSONResponseDefault('OK', 'Login berhasil');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function register()
    {
        try {
            $failed = getGet('failed');

            if (isLogin()) {
                return redirect(base_url('dashboard'));
            }

            $data = array();
            $data['failed'] = removeSymbol($failed);

            return $this->load->view('auth/register', $data);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return redirect(base_url('auth/register?failed=' . $ex->getMessage()));
        }
    }

    public function logout()
    {
        destroySession();

        return redirect(base_url('auth/login'));
    }

    public function forgot()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard'));
        }

        return $this->load->view('auth/forgot');
    }

    public function process_forgot()
    {
        try {
            $this->db->trans_begin();

            if (!get_recaptcha_response()) {
                throw new Exception('Silahkan konfirmasi bahwa anda bukanlah Robot');
            } else if (isLogin()) {
                throw new Exception('Anda telah login');
            }

            $email = getPost('email');

            if ($email == null) {
                throw new Exception('Alamat email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Alamat email tidak valid');
            }

            $get = $this->msusers->get(array(
                'email' => $email,
                'merchantid' => null
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Alamat email tidak terdaftar');
            }

            $row = $get->row();

            $getrequest = $this->passwordrequestreset->get(array(
                'userid' => $row->id
            ));

            if ($getrequest->num_rows() > 0) {
                $rowrequest = $getrequest->row();

                $createddate = $rowrequest->createddate;
                $teenminuteslater = date('Y-m-d H:i:s', strtotime('+10 minutes', strtotime($createddate)));

                if ($teenminuteslater > getCurrentDate()) {
                    throw new Exception('Silahkan tunggu beberapa saat untuk melakukan permintaan reset password');
                }

                $this->passwordrequestreset->delete(array(
                    'userid' => $rowrequest->userid
                ));
            }

            $token = array(
                'userid' => $row->id,
                'email' => $email,
                'date' => getCurrentDate(),
            );

            $token = stringEncryption('encrypt', json_encode($token));

            $insert = array();
            $insert['userid'] = $row->id;
            $insert['token'] = $token;
            $insert['createddate'] = getCurrentDate();

            $this->passwordrequestreset->insert($insert);

            $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, 'Server PPOB & SMM', 'Reset Password Akun Server PPOB & SMM', $this->load->view('auth/reset/verification', array(
                'token' => $token,
                'name' => $row->name,
            ), true));

            if ($sendMail == false) {
                throw new Exception('Gagal mengirim email reset password');
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan permintaan reset password');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Silahkan cek email anda untuk melakukan reset password');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function reset_password()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard'));
        }

        $token = getGet('token');
        $_token = $token;

        if ($token == null) {
            return redirect(base_url('auth/login?failed=Token tidak valid'));
        }

        $token = stringEncryption('decrypt', $token);
        $token = json_decode($token);

        if ($token == null) {
            return redirect(base_url('auth/login?failed=Token tidak valid'));
        }

        $get = $this->passwordrequestreset->get(array(
            'userid' => $token->userid,
            'createddate' => $token->date
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('auth/login'));
        }

        $row = $get->row();

        $createddate = $row->createddate;
        $teenminuteslater = date('Y-m-d H:i:s', strtotime('+10 minutes', strtotime($createddate)));

        if ($teenminuteslater < getCurrentDate()) {
            return redirect(base_url('auth/login?failed=Token sudah tidak berlaku'));
        }

        $data = array();
        $data['token'] = $_token;

        return $this->load->view('auth/reset_password', $data);
    }

    public function process_reset_password()
    {
        try {
            $this->db->trans_begin();

            if (isLogin()) {
                throw new Exception('Anda sudah login');
            }

            $token = stringEncryption('decrypt', getPost('token'));
            $password = getPost('password');

            if ($token == null) {
                throw new Exception('Token tidak valid');
            } else if ($password == null) {
                throw new Exception('Password wajib diisi');
            } else if (!password_strength_check($password)) {
                throw new Exception('Kata sandi minimal memiliki 8 karakter atau lebih dengan campuran huruf, angka & simbol');
            } else {
                $token = json_decode($token);

                if ($token == null) {
                    throw new Exception('Token tidak valid');
                }
            }

            $get = $this->passwordrequestreset->get(array(
                'userid' => $token->userid,
                'createddate' => $token->date
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Token tidak valid');
            }

            $row = $get->row();

            $createddate = $row->createddate;
            $teenminuteslater = date('Y-m-d H:i:s', strtotime('+10 minutes', strtotime($createddate)));

            if ($teenminuteslater < getCurrentDate()) {
                throw new Exception('Token sudah tidak berlaku');
            }

            $update = array();
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);

            $this->msusers->update(array(
                'id' => $row->userid
            ), $update);

            $this->passwordrequestreset->delete(array(
                'id' => $row->id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal memperbarui password');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Password berhasil diperbarui');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}