<?php defined('BASEPATH') or die('No direct script access allowed!'); ?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <div class="page-title d-flex flex-column me-3">
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Detail Member</h1>
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('users/data') ?>" class="text-gray-600 text-hover-primary">Data Pengguna</a>
            </li>
            <li class="breadcrumb-item text-gray-500">Detail Member</li>
        </ul>
    </div>
    <div>
        <a href="<?= base_url('users/data') ?>" class="btn btn-secondary fw-bold">
            <i class="fa fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <!-- Header: Judul + Buttons -->
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div class="card-title">
                        <h3>Detail Member</h3>
                    </div>
                    <div class="d-flex gap-2 flex-wrap align-items-center">
                        <button class="btn btn-warning btn-sm"
                            onclick="decreaseBalance('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-dollar me-1"></i> Kurangi Saldo
                        </button>
                        <button class="btn btn-info btn-sm"
                            onclick="changeRole('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-user me-1"></i> Ubah Hak Akses
                        </button>
                        <button class="btn btn-primary btn-sm"
                            onclick="changePassword('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-key me-1"></i> Ubah Password
                        </button>
                        <button class="btn btn-success btn-sm"
                            onclick="changePIN('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-key me-1"></i> Ubah PIN
                        </button>
                        <button class="btn btn-danger btn-sm"
                            onclick="deleteAccount('<?= stringEncryption('encrypt', $user->id) ?>')">
                            <i class="fa fa-trash me-1"></i> Hapus
                        </button>
                    </div>
                </div>

                <!-- Body: Nav + Panes -->
                <div class="card-body pt-0">
                    <!-- Nav tabs -->

                    <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x fs-6 fw-semibold mt-6 mb-8 gap-2">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4 active show"
                                data-bs-toggle="tab" href="#detail-tab" aria-selected="true" role="tab">
                                <i class="ki-duotone ki-calendar-8 fs-4 me-1"><span
                                        class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="18" height="18"
                                            viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3"
                                                d="M16.5 9C16.5 13.125 13.125 16.5 9 16.5C4.875 16.5 1.5 13.125 1.5 9C1.5 4.875 4.875 1.5 9 1.5C13.125 1.5 16.5 4.875 16.5 9Z"
                                                fill="currentColor" />
                                            <path
                                                d="M9 16.5C10.95 16.5 12.75 15.75 14.025 14.55C13.425 12.675 11.4 11.25 9 11.25C6.6 11.25 4.57499 12.675 3.97499 14.55C5.24999 15.75 7.05 16.5 9 16.5Z"
                                                fill="currentColor" />
                                            <rect x="7" y="6" width="4" height="4" rx="2" fill="currentColor" />
                                        </svg>
                                    </span>
                                </i> Detail
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4" data-bs-toggle="tab"
                                href="#mutasi-tab">
                                <i class="ki-duotone ki-calendar-8 fs-4 me-1"><span class="path1"></span><span
                                        class="path2">
                                        <span class="svg-icon svg-icon-muted svg-icon-2hx"><svg width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.3"
                                                    d="M11 11H13C13.6 11 14 11.4 14 12V21H10V12C10 11.4 10.4 11 11 11ZM16 3V21H20V3C20 2.4 19.6 2 19 2H17C16.4 2 16 2.4 16 3Z"
                                                    fill="currentColor" />
                                                <path
                                                    d="M21 20H8V16C8 15.4 7.6 15 7 15H5C4.4 15 4 15.4 4 16V20H3C2.4 20 2 20.4 2 21C2 21.6 2.4 22 3 22H21C21.6 22 22 21.6 22 21C22 20.4 21.6 20 21 20Z"
                                                    fill="currentColor" />
                                            </svg>
                                        </span></i> Mutasi Saldo
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link text-active-primary d-flex align-items-center pb-4" data-bs-toggle="tab"
                                href="#riwayat-login-tab">
                                <i class="ki-duotone ki-security-user fs-4 me-1">
                                    <span class="svg-icon svg-icon-muted svg-icon-2hx">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path opacity="0.3" d="M12 22C13.6569 22 15 20.6569 15 19C15 17.3431 13.6569 16 12 16C10.3431 16 9 17.3431 9 19C9 20.6569 10.3431 22 12 22Z" fill="currentColor" />
                                            <path d="M19 15V18C19 18.6 18.6 19 18 19H6C5.4 19 5 18.6 5 18V15C5 14.4 5.4 14 6 14H18C18.6 14 19 14.4 19 15ZM17 21V22H7V21C7 20.4 7.4 20 8 20H16C16.6 20 17 20.4 17 21Z" fill="currentColor" />
                                            <path d="M12 13C9.8 13 8 11.2 8 9V5C8 2.8 9.8 1 12 1C14.2 1 16 2.8 16 5V9C16 11.2 14.2 13 12 13ZM10 5V9C10 10.1 10.9 11 12 11C13.1 11 14 10.1 14 9V5C14 3.9 13.1 3 12 3C10.9 3 10 3.9 10 5Z" fill="currentColor" />
                                        </svg>
                                    </span>
                                </i> Riwayat Login
                            </a>
                        </li>
                    </ul>
                    <!-- Tab content -->
                    <div class="tab-content">
                        <!-- Detail Pane -->
                        <div class="tab-pane fade show active" id="detail-tab" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Semua field detail sisi kiri -->
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">ID Akun</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= $user->id ?></div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Nomor Handphone</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= $user->phonenumber ?? '-' ?></div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Status Email</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <?php if ($user->isemailverified == 1): ?>
                                                <span class="badge badge-success">Terverifikasi</span>
                                            <?php else: ?>
                                                <span class="badge badge-danger">Belum Diverifikasi</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Saldo (Balance)</label>
                                        <div class="fw-bold fs-6 text-gray-800">Rp <?= IDR($user->balance) ?></div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Transaksi Terakhir</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <?= $lastTransaction ? DateFormat($lastTransaction->createddate, 'd F Y H:i:s') : '-' ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <!-- Semua field detail sisi kanan -->
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Nama</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <?= $user->name ?>
                                        </div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Email</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= $user->email ?></div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Hak Akses</label>
                                        <div class="fw-bold fs-6 text-gray-800">
                                            <span class="badge badge-primary "><?= $user->rolename ?? 'Member' ?></span>
                                        </div>
                                    </div>
                                    <div class="mb-7">
                                        <label class="fw-semibold fs-6 mb-2">Total Transaksi</label>
                                        <div class="fw-bold fs-6 text-gray-800"><?= IDR($user->transactiontotal ?? 0) ?>
                                            Transaksi</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mutasi Saldo Pane -->
                        <div class="tab-pane fade" id="mutasi-tab" role="tabpanel">
                            <div class="card">

                                <div class="table-responsive">
                                    <table class="table table-striped table-row-bordered gy-5 datatables-mutasi-saldo">
                                        <thead>
                                            <tr class="fw-semibold fs-6 text-muted">
                                                <th>Tanggal</th>
                                                <th>Tipe</th>
                                                <th>Jumlah</th>
                                                <th>Saldo Sebelum</th>
                                                <th>Saldo Sesudah</th>
                                                <th>Kode Transaksi</th>
                                                <th>Kode Deposit</th>
                                                <th>Deskripsi</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Riwayat Login Pane -->
                        <div class="tab-pane fade" id="riwayat-login-tab" role="tabpanel">
                            <div class="card">
                                <div class="table-responsive">
                                    <table class="table table-striped table-row-bordered gy-5 datatables-riwayat-login">
                                        <thead>
                                            <tr class="fw-semibold fs-6 text-muted">
                                                <th>Tanggal</th>
                                                <th>IP Address</th>
                                                <th>User Agent</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- /.tab-content -->
            </div> <!-- /.card-body -->
        </div> <!-- /.card -->
    </div>
</div>


<script>
    let mutasiTableInitialized = false;
    let riwayatLoginTableInitialized = false;

    // Initialize DataTable for Mutasi Saldo when tab is shown
    $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function(e) {
        if (e.target.getAttribute('href') === '#mutasi-tab' && !mutasiTableInitialized) {
            $('.datatables-mutasi-saldo').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url('users/data/history/balance/datatables') ?>',
                    method: 'POST',
                    data: {
                        id: '<?= $encrypted_id ?>'
                    }
                },
                columns: [{
                        data: 0
                    }, // Tanggal
                    {
                        data: 1
                    }, // Tipe
                    {
                        data: 2
                    }, // Jumlah
                    {
                        data: 3
                    }, // Saldo Sebelum
                    {
                        data: 4
                    }, // Saldo Sesudah
                    {
                        data: 5
                    }, // Kode Transaksi
                    {
                        data: 6
                    }, // Kode Deposit
                    {
                        data: 7
                    } // Deskripsi
                ]
            });
            mutasiTableInitialized = true;
        }

        // Initialize DataTable for Riwayat Login when tab is shown
        if (e.target.getAttribute('href') === '#riwayat-login-tab' && !riwayatLoginTableInitialized) {
            $('.datatables-riwayat-login').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                stateSave: true,
                ordering: false,
                ajax: {
                    url: '<?= base_url('users/data/history/login/datatables') ?>',
                    method: 'POST',
                    data: {
                        id: '<?= $encrypted_id ?>'
                    }
                },
                columns: [{
                        data: 0
                    }, // Tanggal
                    {
                        data: 1
                    }, // IP Address
                    {
                        data: 2
                    } // User Agent
                ]
            });
            riwayatLoginTableInitialized = true;
        }
    });

    // Functions for actions
    function changePIN(id) {
        $.ajax({
            url: '<?= base_url('users/data/change/pin') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function changePassword(id) {
        $.ajax({
            url: '<?= base_url('users/data/change/password') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    }

    function decreaseBalance(id) {
        $.ajax({
            url: '<?= base_url('users/data/decrease/balance') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function changeRole(id) {
        $.ajax({
            url: '<?= base_url('users/data/change/role') ?>',
            method: 'POST',
            dataType: 'json',
            data: {
                id: id
            },
            success: function(response) {
                if (response.RESULT == 'OK') {
                    $('#ModalGlobal').html(response.CONTENT);
                    $('#ModalGlobal').modal('show');
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        })
    }

    function deleteAccount(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Pengguna akan dihapus, pengguna akan menerima pesan akun telah dihapus pada saat login',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url('users/data/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.href =
                                    '<?= base_url('users/data') ?>';
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                });
            }
        });
    }
</script>