<?php
defined('BASEPATH') or die('No direct script access allowed!');


use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

/**
 * @property MsUsers $msusers
 * @property HistoryDecreaseSaldo $historydecreasesaldo
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property MsRole $msrole
 * @property HistoryBalance $historybalance
 * @property TrOrder $trorder
 * @property HistoryLogin $historylogin
 */
class Data extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('HistoryD<PERSON>reaseSaldo', 'historydecreasesaldo');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('Historylogin', 'historylogin');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->msrole->select('a.id, a.rolename')
            ->where(array(
                'createdby' => getCurrentIdUser()
            ))
            ->get()
            ->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Data Pengguna';
        $data['content'] = 'user/data/index';
        $data['role'] = $get;

        return $this->load->view('master', $data);
    }

    public function datatables_users()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();
                $email = getPost('email');
                $role = getPost('role');
                $status = getPost('status');

                $datatable = $this->datatables->make('MsUsers', 'QueryDatatables', 'SearchDatatables');

                $filter = getPost('filter');

                if ($filter == false) {
                    $where = array(
                        'merchantid' => getCurrentIdUser(),
                        'isdeleted' => null,
                    );
                } else {
                    $where = array(
                        'merchantid' => getCurrentIdUser(),
                        'isdeleted' => null,
                    );
                }

                if ($email != null) {
                    $where['email'] = $email;
                }

                if ($role != null) {
                    $where['roleid'] = $role;
                }

                if ($status != null) {
                    if ($status == 'T') {
                        $where['isemailverified'] = 1;
                    } else {
                        $where['isemailverified'] = null;
                    }
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    $lastOrder = $this->trorder->limit(1)->order_by('createddate', 'DESC')
                        ->get(array(
                            'a.merchantid_order' => getCurrentIdUser(),
                            'a.userid' => $value->id,
                        ))->row();

                    $detail = array();

                    $buttonverifikasi = '';
                    if ($value->isemailverified == 1) {
                        $isemailverified = "<span class=\"badge badge-success\">Terverifikasi</span>";
                    } else {
                        $isemailverified = "<span class=\"badge badge-danger\">Belum Diverifikasi</span>";
                        $buttonverifikasi = "<div class=\"menu-item px-3\">
                            <button type=\"button\" class=\"btn btn-primary btn-sm mb-1 w-100\" onclick=\"verifikasiEmail('" . stringEncryption('encrypt', $value->id) . "')\">
                                <i class=\"fa fa-envelope\"></i>
                                <span>Verifikasi Email</span>
                            </button>
                        </div>";
                    }

                    $detail[] = $value->rolename ?? 'User';
                    $detail[] = "<b>$value->name</b><br>$value->email";
                    $detail[] = $value->phonenumber ?? '-';
                    $detail[] = "Rp " . IDR($value->balance);
                    $detail[] = $isemailverified;
                    $detail[] = ($lastOrder != null ? "<b>" . date('d F Y H:i:s', strtotime($lastOrder->createddate)) . "</b>" : '-') . "<br>" . IDR($value->transactiontotal) . " Total Transaksi";
                    $detail[] = "                        
                         <div class=\"menu-item px-3\">
                            <button type=\"button\" class=\"btn btn-dark btn-sm mb-1 w-100\" onclick=\"detailMember('" . stringEncryption('encrypt', $value->id) . "')\">
                                <i class=\"fa fa-user\"></i>
                                <span>Detail Member</span>
                            </button>
                        </div>

                        $buttonverifikasi ";


                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function change_pin()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/change_pin', array('user' => $row), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_pin()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $pin = getPost('pin');
            $confirmpin = getPost('confirmpin');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi Baru wajib diisi');
            } else if (!is_numeric($pin) || strlen($pin) != 6) {
                throw new Exception('PIN Transaksi Baru harus berupa angka dan berjumlah 6 digit');
            } else if ($confirmpin == null) {
                throw new Exception('Konfirmasi PIN Transaksi Baru wajib diisi');
            } else if (!is_numeric($confirmpin) || strlen($confirmpin) != 6) {
                throw new Exception('Konfirmasi PIN Transaksi Baru harus berupa angka dan berjumlah 6 digit');
            } else if ($pin != $confirmpin) {
                throw new Exception('PIN Transaksi yang anda masukkan tidak sesuai');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->total(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['pin'] = stringEncryption('encrypt', $pin);

            $this->msusers->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah PIN Transaksi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'PIN Transaksi berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function change_password()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/change_password', array('user' => $row), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_password()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $password = getPost('password');
            $confirmpassword = getPost('confirmpassword');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            } else if ($password == null) {
                throw new Exception('Password Baru wajib diisi');
            } else if ($confirmpassword == null) {
                throw new Exception('Konfirmasi Password Baru wajib diisi');
            } else if (strlen($password) < 8) {
                throw new Exception('Password Baru minimal 8 karakter');
            } else if ($password != $confirmpassword) {
                throw new Exception('Password yang anda masukkan tidak sesuai');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->total(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);

            $this->msusers->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah password');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Password berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_user()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $getrole = $this->msrole->get(array(
                'a.createdby' => getCurrentIdUser()
            ));

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/add_user', array(
                    'datarole' => $getrole->result()
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_user()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $name = getPost('name');
            $email = getPost('email');
            $password = getPost('password');
            $confirmpassword = getPost('confirmpassword');
            $pin = getPost('pin');
            $roleid = getPost('roleid');

            if ($name == null) {
                throw new Exception('Nama wajib diisi');
            } else if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email yang anda masukkan tidak valid');
            } else if ($password == null) {
                throw new Exception('Password wajib diisi');
            } else if (strlen($password) < 8) {
                throw new Exception('Password minimal 8 karakter');
            } else if ($confirmpassword == null) {
                throw new Exception('Konfirmasi Password wajib diisi');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            } else if (!is_numeric($pin)) {
                throw new Exception('PIN Transaksi harus berupa angka');
            } else if (strlen($pin) != 6) {
                throw new Exception('PIN Transaksi harus berjumlah 6 digit');
            } else if ($roleid == null) {
                throw new Exception('Pilih salah satu hak akses');
            } else {
                $name = removeSymbol($name);
            }

            $get = $this->msusers->total(array(
                'email' => $email,
                'merchantid' => getCurrentIdUser()
            ));

            if ($get > 0) {
                throw new Exception('Alamat email yang anda masukkan telah terdaftar');
            }

            $validate_role = $this->msrole->total(array(
                'id' => $roleid,
                'a.createdby' => getCurrentIdUser()
            ));

            if ($validate_role == 0) {
                throw new Exception('Hak akses yang anda pilih tidak tersedia');
            }

            if ($password != $confirmpassword) {
                throw new Exception('Password yang anda masukkan tidak sesuai');
            }

            $insert = array();
            $insert['name'] = $name;
            $insert['email'] = $email;
            $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
            $insert['role'] = 'User';
            $insert['merchantid'] = getCurrentIdUser();
            $insert['pin'] = stringEncryption('encrypt', $pin);
            $insert['isemailverified'] = 1;
            $insert['roleid'] = $roleid;

            $this->msusers->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan pengguna');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Pengguna berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_user()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->total(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isdeleted'] = 1;

            $this->msusers->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function deleted_user()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Data Pengguna Dihapus';
        $data['content'] = 'user/data/deleted_user';

        return $this->load->view('master', $data);
    }

    public function datatables_deleted_user()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatable = $this->datatables->make('MsUsers', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'merchantid' => getCurrentIdUser(),
                    'isdeleted' => 1,
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    $detail[] = $value->name;
                    $detail[] = $value->email;
                    $detail[] = IDR($value->balance);
                    $detail[] = "<button type=\"button\" class=\"btn btn-warning btn-sm\" onclick=\"restoreAccount('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-repeat\"></i>
                        <span>Restore</span>
                    </button>";

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function decrease_balance()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'merchantid' => getCurrentIdUser(),
                'id' => $id,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/decrease_balance', array(
                    'row' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function change_role()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'merchantid' => getCurrentIdUser(),
                'id' => $id,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/change_role', array(
                    'user' => $row,
                    'role' => $this->msrole->result(array('createdby' => getCurrentIdUser()))
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_role()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);
            $roleid = getPost('role');

            if ($roleid == null) {
                throw new Exception('Role tidak boleh kosong');
            }

            $get = $this->msusers->total(array(
                'merchantid' => getCurrentIdUser(),
                'id' => $id,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $role = $this->msrole->total(array(
                'createdby' => getCurrentIdUser(),
                'id' => $roleid
            ));

            if ($role == 0) {
                throw new Exception('Role tidak ditemukan');
            }

            $this->msusers->update(array(
                'id' => $id
            ), array(
                'roleid' => $roleid
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah role');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Role berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_decrease_balance()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $balance = getPost('balance');

            if ($balance == null || $balance == 0) {
                throw new Exception('Saldo tidak boleh kosong');
            } else if (!is_numeric($balance)) {
                throw new Exception('Saldo harus berupa angka');
            }

            $get = $this->msusers->get(array(
                'merchantid' => getCurrentIdUser(),
                'id' => $id,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            if ($balance > $row->balance) {
                throw new Exception('Saldo tidak mencukupi');
            }

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = $id;
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $balance;
            $inserthistorybalance['currentbalance'] = $row->balance;
            $inserthistorybalance['description'] = 'Pengurangan Saldo';
            $inserthistorybalance['createdby'] = $id;
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $row->balance - $balance;

            $this->msusers->update(array(
                'id' => $id
            ), $update);

            $insert = array();
            $insert['userid'] = $id;
            $insert['nominal'] = $balance;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->historydecreasesaldo->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengurangi saldo');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Saldo berhasil dikurangi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_restore_user()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->total(array(
                'merchantid' => getCurrentIdUser(),
                'id' => $id,
                'isdeleted' => 1,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isdeleted'] = null;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msusers->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengembalikan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Akun berhasil dikembalikan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_verifikasi_email()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->total(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['isemailverified'] = 1;

            $this->msusers->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal memverifikasi email');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Email berhasil diverifikasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_export_excel()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $currentiduser = getCurrentIdUser();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', "Laporan Data Pengguna");
        $sheet->mergeCells('A1:F1');
        $sheet->getStyle('A1')->applyFromArray([
            'font' => ['bold' => true, 'size' => 14],
            'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
        ]);

        // Set header values
        $sheet->setCellValue('A2', "No")
            ->setCellValue('B2', "Nama")
            ->setCellValue('C2', "Username")
            ->setCellValue('D2', "Email")
            ->setCellValue('E2', "Nomor Handphone")
            ->setCellValue('F2', "Jumlah Saldo")
            ->setCellValue('G2', "Status Email");

        // Apply styles to header
        $headerStyleArray = [
            'font' => ['bold' => true],
            'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
        ];
        $sheet->getStyle('A2:G2')->applyFromArray($headerStyleArray);

        $getdata = $this->msusers->select('a.*')
            ->where(array(
                'a.merchantid' => $currentiduser,
                'a.isdeleted' => null,
            ))
            ->get()
            ->result();

        $rowstart = 3;

        foreach ($getdata as $key => $value) {
            $sheet->setCellValue('A' . $rowstart, $key + 1)
                ->setCellValue('B' . $rowstart, $value->name)
                ->setCellValue('C' . $rowstart, $value->username ?? '-')
                ->setCellValue('D' . $rowstart, $value->email)
                ->setCellValueExplicit('E' . $rowstart, $value->phonenumber ?? '-', \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING)
                ->setCellValue('F' . $rowstart, IDR($value->balance))
                ->setCellValue('G' . $rowstart, $value->isemailverified == 1 ? 'Terverifikasi' : 'Belum Diverifikasi');

            // Align Jumlah Saldo to the left
            $sheet->getStyle('F' . $rowstart)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            // Set color based on status email verification
            if ($value->isemailverified == 1) {
                $sheet->getStyle('G' . $rowstart)->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => ['argb' => 'FF00FF00'], // Green
                    ],
                ]);
            } else {
                $sheet->getStyle('G' . $rowstart)->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => ['argb' => 'FFFF0000'], // Red
                    ],
                ]);
            }

            $rowstart++;
        }

        // Auto size columns
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Apply border styles
        $sheet->getStyle("A1:G" . ($rowstart - 1))->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                ],
            ],
        ]);

        $writer = new Xlsx($spreadsheet);
        $filename = "Laporan Data Pengguna";

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
    }

    public function history_balance()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/history_balance', array(
                    'user' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_history_balance()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatable = $this->datatables->make('HistoryBalance', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => stringEncryption('decrypt', getPost('id'))
                );

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->type == 'IN' ? 'Pemasukan' : 'Pengeluaran';
                    $detail[] = IDR($value->nominal);
                    $detail[] = IDR($value->currentbalance);
                    $detail[] = IDR($value->type == 'IN' ? $value->currentbalance + $value->nominal : $value->currentbalance - $value->nominal);
                    $detail[] = $value->clientcode ?? '-';
                    $detail[] = $value->depositcode ?? '-';
                    $detail[] = $value->description ?? '-';

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }



    public function detail_member_page($encrypted_id = null)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        if ($encrypted_id == null) {
            return redirect(base_url('users/data'));
        }

        try {
            $id = stringEncryption('decrypt', $encrypted_id);

            $get = $this->msusers->select('a.*, b.rolename')
                ->join('msrole b', 'b.id = a.roleid', 'LEFT')
                ->get(array(
                    'a.id' => $id,
                    'a.merchantid' => getCurrentIdUser(),
                    'a.isdeleted' => null,
                ));

            if ($get->num_rows() == 0) {
                return redirect(base_url('users/data'));
            }

            $row = $get->row();

            // Get last transaction
            $lastTransaction = $this->trorder->limit(1)->order_by('createddate', 'DESC')
                ->get(array(
                    'a.merchantid_order' => getCurrentIdUser(),
                    'a.userid' => $row->id,
                ))->row();

            $data = array();
            $data['title'] = 'Server PPOB & SMM - Detail Customer';
            $data['content'] = 'user/data/detail_member_page';
            $data['user'] = $row;
            $data['lastTransaction'] = $lastTransaction;
            $data['encrypted_id'] = $encrypted_id;

            return $this->load->view('master', $data);
        } catch (Exception $ex) {
            return redirect(base_url('users/data'));
        }
    }

    public function history_login()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'id' => $id,
                'merchantid' => getCurrentIdUser(),
                'isdeleted' => null,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/data/history_login', array(
                    'user' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_history_login()
    {
        try {
            if (! (isLogin() && isUser() && getCurrentUser()->licenseid)) {
                throw new Exception('Access denied');
            }

            $userid = stringEncryption('decrypt', getPost('id'));
            $where  = ['a.userid' => $userid];

            $datatable = $this->datatables->make('HistoryLogin', 'QueryDatatables', 'SearchDatatables');

            $data = [];
            foreach ($datatable->getData($where) as $row) {
                $data[] = [
                    DateFormat($row->createddate, 'd-m-Y H:i:s'),
                    $row->ipaddress,
                    htmlspecialchars($row->useragent, ENT_QUOTES, 'UTF-8'),
                ];
            }

            return $datatable->json($data);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}